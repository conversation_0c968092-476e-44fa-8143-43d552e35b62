'use client'

import { LoginForm } from '@/components/auth/LoginForm'
import { RouteGuard } from '@/components/auth/RouteGuard'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

function LoginContent() {
  const router = useRouter()

  const handleLoginSuccess = () => {
    router.push('/dashboard')
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Or{' '}
            <Link href="/" className="font-medium text-blue-600 hover:text-blue-500">
              return to homepage
            </Link>
          </p>
        </div>

        <LoginForm className="mt-8 space-y-6" onSuccess={handleLoginSuccess} />

        <div className="text-center">
          <p className="text-sm text-gray-600">
            Don&apos;t have an account?{' '}
            <Link href="/register" className="font-medium text-blue-600 hover:text-blue-500">
              Contact your administrator
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

export default function LoginPage() {
  return (
    <RouteGuard requireAuth={false}>
      <LoginContent />
    </RouteGuard>
  )
}
