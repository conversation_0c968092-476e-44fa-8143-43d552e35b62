'use client'

import { AdminDashboard } from '@/components/admin/AdminDashboard'
import { RouteGuard } from '@/components/auth/RouteGuard'
import { UserProfile } from '@/components/auth/UserProfile'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { useAuth } from 'hooks/useAuth'

function DashboardContent() {
  const { user, isAdmin } = useAuth()

  if (isAdmin()) {
    return <AdminDashboard />
  }

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <h1 className="mb-2 text-3xl font-bold text-gray-900">
            Welcome to Ultimate Electrical Designer
          </h1>
          <p className="mb-6 text-gray-600">
            Hello {user?.name}! You&apos;re logged in as a {user?.role.toLowerCase()}.
          </p>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="rounded-lg border border-gray-200 p-4 transition-shadow hover:shadow-md">
              <div className="mb-3 flex items-center">
                <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100">
                  <svg
                    className="h-4 w-4 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900">Heat Tracing</h3>
              </div>
              <p className="text-sm text-gray-600">
                Design and calculate heat tracing systems for industrial applications.
              </p>
            </div>

            <div className="rounded-lg border border-gray-200 p-4 transition-shadow hover:shadow-md">
              <div className="mb-3 flex items-center">
                <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-lg bg-green-100">
                  <svg
                    className="h-4 w-4 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900">Load Calculations</h3>
              </div>
              <p className="text-sm text-gray-600">
                Perform electrical load analysis and sizing calculations.
              </p>
            </div>

            <div className="rounded-lg border border-gray-200 p-4 transition-shadow hover:shadow-md">
              <div className="mb-3 flex items-center">
                <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-lg bg-purple-100">
                  <svg
                    className="h-4 w-4 text-purple-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900">Projects</h3>
              </div>
              <p className="text-sm text-gray-600">
                Manage and organize your electrical design projects.
              </p>
            </div>

            <div className="rounded-lg border border-gray-200 p-4 transition-shadow hover:shadow-md">
              <div className="mb-3 flex items-center">
                <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-lg bg-red-100">
                  <svg
                    className="h-4 w-4 text-red-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900">Cable Sizing</h3>
              </div>
              <p className="text-sm text-gray-600">
                Calculate proper cable sizes for electrical installations.
              </p>
            </div>

            <div className="rounded-lg border border-gray-200 p-4 transition-shadow hover:shadow-md">
              <div className="mb-3 flex items-center">
                <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-lg bg-yellow-100">
                  <svg
                    className="h-4 w-4 text-yellow-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900">Safety Compliance</h3>
              </div>
              <p className="text-sm text-gray-600">
                Ensure designs meet electrical codes and safety standards.
              </p>
            </div>

            <div className="rounded-lg border border-gray-200 p-4 transition-shadow hover:shadow-md">
              <div className="mb-3 flex items-center">
                <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-lg bg-indigo-100">
                  <svg
                    className="h-4 w-4 text-indigo-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900">Documentation</h3>
              </div>
              <p className="text-sm text-gray-600">
                Generate professional reports and technical documentation.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* User Profile Section */}
      <UserProfile />
    </div>
  )
}

export default function DashboardPage() {
  return (
    <RouteGuard requireAuth={true}>
      <DashboardLayout title="Dashboard">
        <DashboardContent />
      </DashboardLayout>
    </RouteGuard>
  )
}
