/**
 * MSW Test Utilities for Playwright E2E Tests
 *
 * This file provides a collection of helper functions designed to simplify
 * interaction with the MSW server during E2E tests. These utilities handle
 * common tasks such as:
 * - Programmatic user login and session management
 * - JWT token handling and storage
 * - Dynamic mocking of API responses for specific test scenarios
 * - Resetting mock state between tests to ensure isolation
 */

import { Page } from '@playwright/test';
import { http, HttpResponse } from 'msw';
import { AuthUtils, MockUser, resetAuthState, UserUtils } from '../mocks/fixtures/auth';
import { resetComponentCategories } from '../mocks/fixtures/componentCategories';
import { resetComponentTypesState as resetComponentTypes } from '../mocks/fixtures/componentTypes';
import { server } from '../mocks/server';

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

/**
 * Logs in a user programmatically and sets the auth token in localStorage.
 *
 * @param page The Playwright Page object.
 * @param user The user object to log in. If not provided, defaults to the first admin user.
 */
export async function loginUser(page: Page, user?: MockUser) {
  const userToLogin = user || UserUtils.findByUsername('admin');
  if (!userToLogin) {
    throw new Error('No user available to log in.');
  }

  const tokens = AuthUtils.createAuthTokens(userToLogin);

  await page.evaluate(
    (token) => {
      localStorage.setItem('auth_token', token);
    },
    tokens.access_token
  );
}

/**
 * Overrides a specific API handler for the duration of a test.
 *
 * @param endpoint The API endpoint to override (e.g., '/api/v1/users').
 * @param method The HTTP method to override (e.g., 'get', 'post').
 * @param response The mock response to return.
 * @param status The HTTP status code of the mock response.
 */
export function overrideApiHandler(
  endpoint: string,
  method: 'get' | 'post' | 'put' | 'delete' | 'patch',
  response: any,
  status: number = 200
) {
  server.use(
    http[method](`${API_BASE}${endpoint}`, () => {
      return HttpResponse.json(response, { status });
    })
  );
}

/**
 * Resets all mock state to its initial condition.
 * This should be called before each test to ensure test isolation.
 */
export function resetMocks() {
  resetAuthState();
  resetComponentCategories();
  resetComponentTypes();
  // Add other state reset functions here as they are created
  server.resetHandlers();
}