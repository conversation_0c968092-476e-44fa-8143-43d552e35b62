{"name": "ultimate-electrical-designer-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@headless-tree/core": "^1.2.1", "@internationalized/date": "^3.8.2", "@origin-space/image-cropper": "^0.1.9", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@remixicon/react": "^4.6.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@types/jsonwebtoken": "^9.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "jsonwebtoken": "^9.0.2", "jwt-decode": "^3.1.2", "lodash-es": "^4.17.21", "lucide-react": "^0.525.0", "next": "^15.4.1", "next-i18next": "15.4.2", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-aria-components": "^1.10.1", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-resizable-panels": "^3.0.3", "sonner": "^2.0.6", "tailwind-merge": "^2.6.0", "tw-animate-css": "^1.3.5", "zustand": "^5.0.6"}, "devDependencies": {"@playwright/test": "^1.53.2", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.10", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-react": "^4.0.0", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.0.1", "eslint": "^9.30.1", "eslint-config-next": "15.3.0", "jsdom": "^23.0.0", "msw": "^2.10.4", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.5.0", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vitest": "^3.2.4"}}