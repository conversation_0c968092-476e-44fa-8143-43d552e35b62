/**
 * Mock Service Worker (MSW) Server Setup for Playwright E2E Tests
 * 
 * This file configures MSW to intercept network requests during Playwright E2E tests,
 * providing comprehensive API mocking that eliminates backend dependencies while
 * maintaining full API fidelity with the actual FastAPI backend.
 */

import { setupServer } from 'msw/node';
import { authHandlers } from './handlers/auth';
import { componentCategoryHandlers } from './handlers/componentCategories';
import { componentHandlers } from './handlers/components';
import { componentTypeHandlers } from './handlers/componentTypes';
import { healthHandlers } from './handlers/health';
import { userHandlers } from './handlers/users';

/**
 * MSW Server instance for Playwright E2E tests
 * 
 * Combines all API handlers to provide comprehensive backend mocking:
 * - Authentication & Authorization (JWT tokens, user sessions)
 * - User Management (CRUD, profiles, admin operations)
 * - Health Checks (ping, detailed metrics, readiness probes)
 * - Component Management (existing CRUD operations)
 * - Component Categories (hierarchical operations, tree management)
 * - Component Types (CRUD with specifications templates)
 */
export const server = setupServer(
  // Authentication endpoints
  ...authHandlers,
  
  // User management endpoints
  ...userHandlers,
  
  // Health check endpoints
  ...healthHandlers,
  
  // Component management endpoints (existing)
  ...componentHandlers,
  
  // Component category endpoints
  ...componentCategoryHandlers,
  
  // Component type endpoints
  ...componentTypeHandlers,
);

/**
 * Start MSW server for test environment
 * Called before Playwright tests begin
 */
export function startMockServer() {
  server.listen({
    onUnhandledRequest: 'warn',
  });
  
  console.log('🚀 MSW Server started for Playwright E2E tests');
}

/**
 * Stop MSW server after tests complete
 * Called after Playwright tests finish
 */
export function stopMockServer() {
  server.close();
  console.log('🛑 MSW Server stopped');
}

/**
 * Reset MSW handlers between tests
 * Ensures test isolation and clean state
 */
export function resetMockServer() {
  server.resetHandlers();
}

/**
 * Configure dynamic handlers for specific test scenarios
 * Allows tests to override default behavior for error scenarios
 */
export function configureMockHandlers(...handlers: Parameters<typeof server.use>) {
  server.use(...handlers);
}

export default server;