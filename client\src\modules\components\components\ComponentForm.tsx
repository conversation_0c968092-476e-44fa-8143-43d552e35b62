'use client'

/**
 * ComponentForm - Form component for creating and editing components
 * Handles validation and submission for component CRUD operations
 */

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, Save, X } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { useComponentCategories, useComponentTypes } from '../api/componentQueries'
import type { ComponentCreate, ComponentRead, ComponentUpdate } from '../types'
import { validateComponent, type ValidationError } from '../utils'

export interface ComponentFormProps {
  component?: ComponentRead
  isEditing?: boolean
  isLoading?: boolean
  onSubmit: (data: ComponentCreate | ComponentUpdate) => void
  onCancel: () => void
  className?: string
}

export function ComponentForm({
  component,
  isEditing = false,
  isLoading = false,
  onSubmit,
  onCancel,
  className = '',
}: ComponentFormProps) {
  const [formData, setFormData] = useState<Partial<ComponentCreate | ComponentUpdate>>({
    name: component?.name || '',
    manufacturer: component?.manufacturer || '',
    model_number: component?.model_number || '',
    description: component?.description || '',
    component_category_id: component?.component_category_id || null,
    component_type_id: component?.component_type_id || null,
    unit_price: component?.unit_price || null,
    currency: component?.currency || 'EUR',
    supplier: component?.supplier || '',
    part_number: component?.part_number || '',
    weight_kg: component?.weight_kg || null,
    is_active: component?.is_active ?? true,
    is_preferred: component?.is_preferred ?? false,
  })

  const [errors, setErrors] = useState<ValidationError[]>([])
  const [isDirty, setIsDirty] = useState(false)

  // Update form data when component prop changes
  useEffect(() => {
    if (component) {
      setFormData({
        name: component.name || '',
        manufacturer: component.manufacturer || '',
        model_number: component.model_number || '',
        description: component.description || '',
        component_category_id: component.component_category_id || null,
        component_type_id: component.component_type_id || null,
        unit_price: component.unit_price || null,
        currency: component.currency || 'EUR',
        supplier: component.supplier || '',
        part_number: component.part_number || '',
        weight_kg: component.weight_kg || null,
        is_active: component.is_active ?? true,
        is_preferred: component.is_preferred ?? false,
      })
      setErrors([])
      setIsDirty(false)
    }
  }, [component])

  // Fetch options
  const { data: categories = [] } = useComponentCategories()
  const { data: types = [] } = useComponentTypes()

  // Handle form field changes
  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    setIsDirty(true)

    // Clear field-specific errors
    setErrors((prev) => prev.filter((error) => error.field !== field))
  }

  // Handle field blur - validate individual field
  const handleBlur = (field: string) => {
    const validation = validateComponent(formData, isEditing)
    const fieldErrors = validation.errors.filter((error) => error.field === field)

    // Update errors for this field only
    setErrors((prev) => [
      ...prev.filter((error) => error.field !== field),
      ...fieldErrors
    ])
  }

  // Validate form
  const validateForm = () => {
    const validation = validateComponent(formData, isEditing)
    setErrors(validation.errors)
    return validation.isValid
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Always validate to show errors to user
    const isValid = validateForm()

    // Only proceed with submission if form is valid AND dirty (has changes)
    if (isValid && isDirty) {
      onSubmit(formData as ComponentCreate | ComponentUpdate)
    }
  }

  // Get field error
  const getFieldError = (field: string) => {
    return errors.find((error) => error.field === field)?.message
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{isEditing ? 'Edit Component' : 'Create New Component'}</CardTitle>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6" role="form" data-testid="component-form">
          {/* Basic Information */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label
                htmlFor="component-name"
                className="mb-1 block text-sm font-medium text-gray-700"
              >
                Component Name *
              </label>
              <input
                id="component-name"
                type="text"
                value={formData.name || ''}
                onChange={(e) => handleChange('name', e.target.value)}
                onBlur={() => handleBlur('name')}
                className={`w-full rounded-lg border px-3 py-2 ${
                  getFieldError('name') ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter component name"
                aria-required="true"
                aria-invalid={!!getFieldError('name')}
                aria-describedby={getFieldError('name') ? 'component-name-error' : undefined}
              />
              {getFieldError('name') && (
                <p id="component-name-error" className="mt-1 text-xs text-red-500">{getFieldError('name')}</p>
              )}
            </div>

            <div>
              <label
                htmlFor="manufacturer"
                className="mb-1 block text-sm font-medium text-gray-700"
              >
                Manufacturer *
              </label>
              <input
                id="manufacturer"
                type="text"
                value={formData.manufacturer || ''}
                onChange={(e) => handleChange('manufacturer', e.target.value)}
                onBlur={() => handleBlur('manufacturer')}
                className={`w-full rounded-lg border px-3 py-2 ${
                  getFieldError('manufacturer') ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter manufacturer"
                aria-required="true"
                aria-invalid={!!getFieldError('manufacturer')}
                aria-describedby={getFieldError('manufacturer') ? 'manufacturer-error' : undefined}
              />
              {getFieldError('manufacturer') && (
                <p id="manufacturer-error" className="mt-1 text-xs text-red-500">{getFieldError('manufacturer')}</p>
              )}
            </div>

            <div>
              <label
                htmlFor="model-number"
                className="mb-1 block text-sm font-medium text-gray-700"
              >
                Model Number *
              </label>
              <input
                id="model-number"
                type="text"
                value={formData.model_number || ''}
                onChange={(e) => handleChange('model_number', e.target.value)}
                onBlur={() => handleBlur('model_number')}
                className={`w-full rounded-lg border px-3 py-2 ${
                  getFieldError('model_number') ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter model number"
                aria-required="true"
                aria-invalid={!!getFieldError('model_number')}
                aria-describedby={getFieldError('model_number') ? 'model-number-error' : undefined}
              />
              {getFieldError('model_number') && (
                <p id="model-number-error" className="mt-1 text-xs text-red-500">{getFieldError('model_number')}</p>
              )}
            </div>

            <div>
              <label htmlFor="category" className="mb-1 block text-sm font-medium text-gray-700">
                Category
              </label>
              <select
                id="category"
                value={formData.component_category_id || ''}
                onChange={(e) =>
                  handleChange(
                    'component_category_id',
                    e.target.value ? Number(e.target.value) : null
                  )
                }
                className="w-full rounded-lg border border-gray-300 px-3 py-2"
              >
                <option value="">Select category</option>
                {categories.map((category) => (
                  <option key={category.value} value={category.value}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="mb-1 block text-sm font-medium text-gray-700">
              Description
            </label>
            <textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className="w-full rounded-lg border border-gray-300 px-3 py-2"
              placeholder="Enter component description"
            />
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div>
              <label htmlFor="unit-price" className="mb-1 block text-sm font-medium text-gray-700">
                Unit Price
              </label>
              <input
                id="unit-price"
                type="number"
                step="0.01"
                value={formData.unit_price || ''}
                onChange={(e) =>
                  handleChange('unit_price', e.target.value ? Number(e.target.value) : null)
                }
                className="w-full rounded-lg border border-gray-300 px-3 py-2"
                placeholder="0.00"
              />
            </div>

            <div>
              <label htmlFor="currency" className="mb-1 block text-sm font-medium text-gray-700">
                Currency
              </label>
              <select
                id="currency"
                value={formData.currency || 'EUR'}
                onChange={(e) => handleChange('currency', e.target.value)}
                className="w-full rounded-lg border border-gray-300 px-3 py-2"
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="CAD">CAD</option>
              </select>
            </div>

            <div>
              <label htmlFor="weight" className="mb-1 block text-sm font-medium text-gray-700">
                Weight (kg)
              </label>
              <input
                id="weight"
                type="number"
                step="0.01"
                value={formData.weight_kg || ''}
                onChange={(e) =>
                  handleChange('weight_kg', e.target.value ? Number(e.target.value) : null)
                }
                className="w-full rounded-lg border border-gray-300 px-3 py-2"
                placeholder="0.00"
              />
            </div>
          </div>

          {/* Status */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active || false}
                onChange={(e) => handleChange('is_active', e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
                Active
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_preferred"
                checked={formData.is_preferred || false}
                onChange={(e) => handleChange('is_preferred', e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="is_preferred" className="text-sm font-medium text-gray-700">
                Preferred
              </label>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-3 border-t pt-6">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>

            <Button type="submit" disabled={isLoading} className="min-w-[120px]">
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" data-testid="loading-spinner" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              {isEditing ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
