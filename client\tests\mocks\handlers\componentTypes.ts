/**
 * Component Type API Handlers for MSW
 * 
 * Provides comprehensive mocking for component type management including:
 * - CRUD operations with category relationship validation
 * - Specifications template management
 * - Category-based filtering and search
 * - Pagination and advanced filtering
 */

import { http, HttpResponse } from 'msw';
import { AuthErrors, getUserFromRequest, isAuthenticatedUser } from '../fixtures/auth';
import {
  ComponentType,
  createType,
  deleteType,
  findTypeById,
  getPaginatedTypes,
  getTypesByCategory,
  mockComponentTypes,
  searchTypes,
  updateSpecificationsTemplate,
  updateType,
} from '../fixtures/componentTypes';

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export const componentTypeHandlers = [
  /**
   * POST /api/v1/component-types/
   * Create component type
   */
  http.post(`${API_BASE}/api/v1/component-types/`, async ({ request }) => {
    const currentUser = getUserFromRequest(request);
    
    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 });
    }

    if (!isAuthenticatedUser(currentUser)) {
      return HttpResponse.json(AuthErrors.USER_INACTIVE, { status: 403 });
    }

    const typeData = await request.json() as any;
    
    // Basic validation
    if (!typeData.name) {
      return HttpResponse.json({
        error: "VALIDATION_ERROR",
        detail: "Type name is required",
        status_code: 400
      }, { status: 400 });
    }

    if (!typeData.category_id) {
      return HttpResponse.json({
        error: "VALIDATION_ERROR",
        detail: "Category ID is required",
        status_code: 400
      }, { status: 400 });
    }

    // Check for duplicate names within the same category
    const existing = mockComponentTypes.find((type: ComponentType) =>
      type.name === typeData.name && 
      type.category_id === typeData.category_id
    );
    
    if (existing) {
      return HttpResponse.json({
        error: "TYPE_ALREADY_EXISTS",
        detail: "Component type with this name already exists in this category",
        status_code: 409
      }, { status: 409 });
    }

    try {
      const newType = createType(typeData);
      return HttpResponse.json(newType, { status: 201 });
    } catch (error: any) {
      return HttpResponse.json({
        error: "CREATE_FAILED",
        detail: error.message,
        status_code: 400
      }, { status: 400 });
    }
  }),

  /**
   * GET /api/v1/component-types/{type_id}
   * Get component type by ID
   */
  http.get(`${API_BASE}/api/v1/component-types/:typeId`, ({ request, params }) => {
    const currentUser = getUserFromRequest(request);
    
    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 });
    }

    const typeId = parseInt(params.typeId as string);
    const type = findTypeById(typeId);
    
    if (!type) {
      return HttpResponse.json({
        error: "TYPE_NOT_FOUND",
        detail: `Component type with ID ${typeId} not found`,
        status_code: 404
      }, { status: 404 });
    }

    return HttpResponse.json(type);
  }),

  /**
   * PUT /api/v1/component-types/{type_id}
   * Update component type
   */
  http.put(`${API_BASE}/api/v1/component-types/:typeId`, async ({ request, params }) => {
    const currentUser = getUserFromRequest(request);
    
    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 });
    }

    const typeId = parseInt(params.typeId as string);
    const updates = await request.json() as any;
    
    try {
      const updatedType = updateType(typeId, updates);
      return HttpResponse.json(updatedType);
    } catch (error: any) {
      if (error.message.includes('not found')) {
        return HttpResponse.json({
          error: "TYPE_NOT_FOUND",
          detail: error.message,
          status_code: 404
        }, { status: 404 });
      }
      
      return HttpResponse.json({
        error: "UPDATE_FAILED",
        detail: error.message,
        status_code: 400
      }, { status: 400 });
    }
  }),

  /**
   * DELETE /api/v1/component-types/{type_id}
   * Delete component type
   */
  http.delete(`${API_BASE}/api/v1/component-types/:typeId`, ({ request, params }) => {
    const currentUser = getUserFromRequest(request);
    
    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 });
    }

    const typeId = parseInt(params.typeId as string);
    
    try {
      const success = deleteType(typeId);
      
      if (!success) {
        return HttpResponse.json({
          error: "TYPE_NOT_FOUND",
          detail: `Component type with ID ${typeId} not found`,
          status_code: 404
        }, { status: 404 });
      }
      
      return new HttpResponse(null, { status: 204 });
    } catch (error: any) {
      if (error.message.includes('has dependencies')) {
        return HttpResponse.json({
          error: "TYPE_HAS_DEPENDENCIES",
          detail: error.message,
          status_code: 409
        }, { status: 409 });
      }
      
      return HttpResponse.json({
        error: "DELETE_FAILED",
        detail: error.message,
        status_code: 400
      }, { status: 400 });
    }
  }),

  /**
   * GET /api/v1/component-types/
   * List component types with pagination and filtering
   */
  http.get(`${API_BASE}/api/v1/component-types/`, ({ request }) => {
    const currentUser = getUserFromRequest(request);
    
    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const size = parseInt(url.searchParams.get('size') || '20');
    const searchTerm = url.searchParams.get('search_term');
    const categoryId = url.searchParams.get('category_id');
    const isActive = url.searchParams.get('is_active');
    const hasSpecificationsTemplate = url.searchParams.get('has_specifications_template');
    
    const skip = (page - 1) * size;
    
    let result;
    if (searchTerm) {
      result = searchTypes(searchTerm, skip, size, {
        category_id: categoryId ? parseInt(categoryId) : undefined,
        is_active: isActive ? isActive === 'true' : undefined,
        has_specifications_template: hasSpecificationsTemplate ? hasSpecificationsTemplate === 'true' : undefined,
      });
    } else {
      result = getPaginatedTypes(skip, size, {
        category_id: categoryId ? parseInt(categoryId) : undefined,
        is_active: isActive ? isActive === 'true' : undefined,
        has_specifications_template: hasSpecificationsTemplate ? hasSpecificationsTemplate === 'true' : undefined,
      });
    }

    return HttpResponse.json({
      component_types: result.types,
      total: result.total,
      page,
      size,
      total_pages: Math.ceil(result.total / size),
    });
  }),

  /**
   * GET /api/v1/component-types/by-category/{category_id}
   * Get component types by category
   */
  http.get(`${API_BASE}/api/v1/component-types/by-category/:categoryId`, ({ request, params }) => {
    const currentUser = getUserFromRequest(request);
    
    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 });
    }

    const categoryId = parseInt(params.categoryId as string);
    const url = new URL(request.url);
    const includeInactive = url.searchParams.get('include_inactive') === 'true';
    const skip = parseInt(url.searchParams.get('skip') || '0');
    const limit = parseInt(url.searchParams.get('limit') || '100');
    
    const types = getTypesByCategory(categoryId, includeInactive, skip, limit);
    
    // Return summary format
    const summaryTypes = types.map((type: ComponentType) => ({
      id: type.id,
      name: type.name,
      description: type.description,
      category_id: type.category_id,
      is_active: type.is_active,
      has_specifications_template: !!type.specifications_template,
      created_at: type.created_at,
    }));

    return HttpResponse.json(summaryTypes);
  }),

  /**
   * PUT /api/v1/component-types/{type_id}/specifications-template
   * Update specifications template for component type
   */
  http.put(`${API_BASE}/api/v1/component-types/:typeId/specifications-template`, async ({ request, params }) => {
    const currentUser = getUserFromRequest(request);
    
    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 });
    }

    const typeId = parseInt(params.typeId as string);
    const template = await request.json() as any;
    
    // Basic template validation
    if (!template || typeof template !== 'object') {
      return HttpResponse.json({
        error: "INVALID_TEMPLATE",
        detail: "Template must be a valid JSON object",
        status_code: 400
      }, { status: 400 });
    }

    try {
      const updatedType = updateSpecificationsTemplate(typeId, template);
      return HttpResponse.json(updatedType);
    } catch (error: any) {
      if (error.message.includes('not found')) {
        return HttpResponse.json({
          error: "TYPE_NOT_FOUND",
          detail: error.message,
          status_code: 404
        }, { status: 404 });
      }
      
      return HttpResponse.json({
        error: "TEMPLATE_UPDATE_FAILED",
        detail: error.message,
        status_code: 400
      }, { status: 400 });
    }
  }),
];