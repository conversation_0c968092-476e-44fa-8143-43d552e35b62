'use client'

/**
 * ComponentFilters - Filter panel component for component search
 * Provides category, type, manufacturer, and other filtering options
 */

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
    Building,
    ChevronDown,
    ChevronUp,
    DollarSign,
    Filter,
    Package,
    Star,
    Tag,
    X,
} from 'lucide-react'
import React, { useState } from 'react'
import { useComponentCategories, useComponentTypes } from '../api/componentQueries'
import type { ComponentCategoryType, ComponentType, ComponentFilterState as FilterType } from '../types'

export interface ComponentFiltersProps {
  filters: FilterType
  onFiltersChange: (filters: FilterType) => void
  onClear?: () => void
  className?: string
  collapsible?: boolean
  defaultExpanded?: boolean
}

export function ComponentFilters({
  filters,
  onFiltersChange,
  onClear,
  className = '',
  collapsible = true,
  defaultExpanded = true,
}: ComponentFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)
  const [expandedSections, setExpandedSections] = useState({
    category: true,
    type: true,
    manufacturer: false,
    price: false,
    status: true,
  })

  // Fetch filter options
  const { data: categories = [] } = useComponentCategories()
  const { data: types = [] } = useComponentTypes(filters.category || undefined)

  // Get unique manufacturers (this would typically come from an API endpoint)
  const manufacturers = [
    'Schneider Electric',
    'ABB',
    'Siemens',
    'General Electric',
    'Eaton',
    'Rockwell Automation',
    'Mitsubishi Electric',
    'Honeywell',
    'Emerson',
    'Phoenix Contact',
    'Weidmuller',
    'WAGO',
    'Omron',
    'Danfoss',
  ]

  // Handle filter changes
  const updateFilter = (key: keyof FilterType, value: any) => {
    const newFilters = { ...filters, [key]: value }

    // Clear component type if category changes
    if (key === 'category' && value !== filters.category) {
      newFilters.component_type = null
    }

    onFiltersChange(newFilters)
  }

  // Toggle section expansion
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  // Count active filters
  const activeFilterCount = Object.values(filters).filter(
    (value) => value !== null && value !== undefined && value !== ''
  ).length

  // Clear all filters
  const handleClearAll = () => {
    onFiltersChange({
      search_term: '',
      category: null,
      component_type: null,
      manufacturer: '',
      is_preferred: null,
      is_active: null,
      min_price: null,
      max_price: null,
      currency: 'EUR',
      stock_status: '',
    })
    onClear?.()
  }

  const FilterSection = ({
    title,
    icon: Icon,
    sectionKey,
    children,
  }: {
    title: string
    icon: any
    sectionKey: keyof typeof expandedSections
    children: React.ReactNode
  }) => (
    <div className="border-b border-gray-200 last:border-b-0">
      <button
        onClick={() => toggleSection(sectionKey)}
        className="flex w-full items-center justify-between p-3 text-left hover:bg-gray-50"
      >
        <div className="flex items-center gap-2">
          <Icon className="h-4 w-4 text-gray-500" />
          <span className="font-medium text-gray-900">{title}</span>
        </div>
        {expandedSections[sectionKey] ? (
          <ChevronUp className="h-4 w-4 text-gray-400" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-400" />
        )}
      </button>

      {expandedSections[sectionKey] && <div className="px-3 pb-3">{children}</div>}
    </div>
  )

  return (
    <div className={`rounded-lg border border-gray-200 bg-white ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 p-4">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-500" />
          <h3 className="font-semibold text-gray-900">Filters</h3>
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {activeFilterCount}
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2">
          {activeFilterCount > 0 && (
            <Button variant="ghost" size="sm" onClick={handleClearAll} className="text-xs">
              Clear all
            </Button>
          )}

          {collapsible && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1"
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          )}
        </div>
      </div>

      {/* Active filters */}
      {activeFilterCount > 0 && (
        <div className="border-b border-gray-200 bg-gray-50 p-3">
          <div className="flex flex-wrap gap-2">
            {filters.category && (
              <Badge variant="outline" className="text-xs">
                Category: {filters.category}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateFilter('category', null)}
                  className="ml-1 h-3 w-3 p-0"
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}

            {filters.component_type && (
              <Badge variant="outline" className="text-xs">
                Type: {filters.component_type}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateFilter('component_type', null)}
                  className="ml-1 h-3 w-3 p-0"
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}

            {filters.manufacturer && (
              <Badge variant="outline" className="text-xs">
                Manufacturer: {filters.manufacturer}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateFilter('manufacturer', '')}
                  className="ml-1 h-3 w-3 p-0"
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}

            {filters.is_preferred !== null && (
              <Badge variant="outline" className="text-xs">
                {filters.is_preferred ? 'Preferred' : 'Not Preferred'}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateFilter('is_preferred', null)}
                  className="ml-1 h-3 w-3 p-0"
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}
          </div>
        </div>
      )}

      {/* Filter sections */}
      {isExpanded && (
        <div>
          {/* Category Filter */}
          <FilterSection title="Category" icon={Tag} sectionKey="category">
            <div className="space-y-2">
              {/* Select element for automated testing */}
              <select
                data-testid="category-filter"
                value={filters.category || ''}
                onChange={(e) => updateFilter('category', e.target.value || null)}
                className="w-full rounded border border-gray-300 px-3 py-2 text-sm"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category.value} value={category.value}>
                    {category.name}
                  </option>
                ))}
              </select>
              
              {/* Radio buttons for better UX */}
              <div className="space-y-2 pt-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="category"
                  checked={filters.category === null}
                  onChange={() => updateFilter('category', null)}
                  className="mr-2"
                />
                <span className="text-sm">All Categories</span>
              </label>
              {categories.map((category) => (
                <label key={category.value} className="flex items-center">
                  <input
                    type="radio"
                    name="category"
                    checked={filters.category === category.value}
                    onChange={() =>
                      updateFilter('category', category.value as ComponentCategoryType)
                    }
                    className="mr-2"
                  />
                  <span className="text-sm">{category.name}</span>
                </label>
              ))}
              </div>
            </div>
          </FilterSection>

          {/* Component Type Filter */}
          <FilterSection title="Component Type" icon={Package} sectionKey="type">
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="component_type"
                  checked={filters.component_type === null}
                  onChange={() => updateFilter('component_type', null)}
                  className="mr-2"
                />
                <span className="text-sm">All Types</span>
              </label>
              {types.map((type) => (
                <label key={type.value} className="flex items-center">
                  <input
                    type="radio"
                    name="component_type"
                    checked={filters.component_type === type.value}
                    onChange={() => updateFilter('component_type', type.value as ComponentType)}
                    className="mr-2"
                  />
                  <span className="text-sm">{type.name}</span>
                </label>
              ))}
            </div>
          </FilterSection>

          {/* Manufacturer Filter */}
          <FilterSection title="Manufacturer" icon={Building} sectionKey="manufacturer">
            <select
              value={filters.manufacturer || ''}
              onChange={(e) => updateFilter('manufacturer', e.target.value)}
              className="w-full rounded border border-gray-300 px-3 py-2 text-sm"
            >
              <option value="">All Manufacturers</option>
              {manufacturers.map((manufacturer) => (
                <option key={manufacturer} value={manufacturer}>
                  {manufacturer}
                </option>
              ))}
            </select>
          </FilterSection>

          {/* Price Range Filter */}
          <FilterSection title="Price Range" icon={DollarSign} sectionKey="price">
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="mb-1 block text-xs text-gray-600">Min Price</label>
                  <input
                    type="number"
                    value={filters.min_price || ''}
                    onChange={(e) =>
                      updateFilter('min_price', e.target.value ? Number(e.target.value) : null)
                    }
                    placeholder="0"
                    className="w-full rounded border border-gray-300 px-2 py-1 text-sm"
                  />
                </div>
                <div>
                  <label className="mb-1 block text-xs text-gray-600">Max Price</label>
                  <input
                    type="number"
                    value={filters.max_price || ''}
                    onChange={(e) =>
                      updateFilter('max_price', e.target.value ? Number(e.target.value) : null)
                    }
                    placeholder="∞"
                    className="w-full rounded border border-gray-300 px-2 py-1 text-sm"
                  />
                </div>
              </div>

              <div>
                <label className="mb-1 block text-xs text-gray-600">Currency</label>
                <select
                  value={filters.currency || 'EUR'}
                  onChange={(e) => updateFilter('currency', e.target.value)}
                  className="w-full rounded border border-gray-300 px-2 py-1 text-sm"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="CAD">CAD</option>
                </select>
              </div>
            </div>
          </FilterSection>

          {/* Status Filter */}
          <FilterSection title="Status" icon={Star} sectionKey="status">
            <div className="space-y-3">
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Preferred Status
                </label>
                <div className="space-y-1">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="is_preferred"
                      checked={filters.is_preferred === null}
                      onChange={() => updateFilter('is_preferred', null)}
                      className="mr-2"
                    />
                    <span className="text-sm">All</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="is_preferred"
                      checked={filters.is_preferred === true}
                      onChange={() => updateFilter('is_preferred', true)}
                      className="mr-2"
                    />
                    <span className="text-sm">Preferred Only</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="is_preferred"
                      checked={filters.is_preferred === false}
                      onChange={() => updateFilter('is_preferred', false)}
                      className="mr-2"
                    />
                    <span className="text-sm">Not Preferred</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Active Status
                </label>
                <div className="space-y-1">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="is_active"
                      checked={filters.is_active === null}
                      onChange={() => updateFilter('is_active', null)}
                      className="mr-2"
                    />
                    <span className="text-sm">All</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="is_active"
                      checked={filters.is_active === true}
                      onChange={() => updateFilter('is_active', true)}
                      className="mr-2"
                    />
                    <span className="text-sm">Active Only</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="is_active"
                      checked={filters.is_active === false}
                      onChange={() => updateFilter('is_active', false)}
                      className="mr-2"
                    />
                    <span className="text-sm">Inactive</span>
                  </label>
                </div>
              </div>
            </div>
          </FilterSection>
        </div>
      )}
    </div>
  )
}
