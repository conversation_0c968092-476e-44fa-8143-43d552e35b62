'use client'

/**
 * Edit Component Page
 * Form for editing existing components
 */

import { Button } from '@/components/ui/button'
import { ComponentForm } from '@/modules/components'
import { useUpdateComponent } from '@/modules/components/api/componentMutations'
import { useComponent } from '@/modules/components/api/componentQueries'
import type { ComponentUpdate } from '@/modules/components/types'
import { ArrowLeft } from 'lucide-react'
import { useParams, useRouter } from 'next/navigation'

export default function EditComponentPage() {
  const params = useParams()
  const router = useRouter()
  const componentId = params?.id ? parseInt(params.id as string) : undefined

  // Fetch component data
  const { data: component, isLoading: isLoadingComponent, error } = useComponent(componentId!)

  // Update mutation
  const updateMutation = useUpdateComponent({
    onSuccess: (component) => {
      router.push(`/components/${component.id}`)
    },
    onError: (error) => {
      alert(`Failed to update component: ${error.message}`)
    },
  })

  // Handle form submission
  const handleSubmit = (data: ComponentUpdate) => {
    updateMutation.mutate({ id: componentId!, component: data })
  }

  // Handle cancel
  const handleCancel = () => {
    router.back()
  }

  // Loading state
  if (isLoadingComponent) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Button variant="outline" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>

          <div className="rounded-lg border border-gray-200 bg-white p-6">
            <div className="animate-pulse">
              <div className="mb-6 h-6 w-1/3 rounded bg-gray-200"></div>
              <div className="space-y-4">
                {Array.from({ length: 8 }).map((_, index) => (
                  <div key={index}>
                    <div className="mb-2 h-4 w-1/4 rounded bg-gray-200"></div>
                    <div className="h-10 w-full rounded bg-gray-200"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error || !component) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Button variant="outline" onClick={handleCancel}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          </div>

          <div className="rounded-lg border border-gray-200 bg-white p-12">
            <div className="text-center">
              <div className="mb-4 text-red-500">
                <svg
                  className="mx-auto h-12 w-12"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-lg font-medium text-gray-900">Component not found</h3>
              <p className="mb-6 text-gray-600">
                {error?.message || 'The requested component could not be found.'}
              </p>
              <Button onClick={handleCancel}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="border-b border-gray-200 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>

              <div>
                <h1 className="text-xl font-bold text-gray-900">Edit Component</h1>
                <p className="text-sm text-gray-600">
                  {component.manufacturer} • {component.model_number}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="mx-auto max-w-4xl px-4 py-6 sm:px-6 lg:px-8">
        <ComponentForm
          component={component}
          isEditing={true}
          isLoading={updateMutation.isPending}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      </div>
    </div>
  )
}
