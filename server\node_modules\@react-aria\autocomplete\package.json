{"name": "@react-aria/autocomplete", "version": "3.0.0-beta.5", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "exports": {"source": "./src/index.ts", "types": ["./dist/types.d.ts", "./src/index.ts"], "import": "./dist/import.mjs", "require": "./dist/main.js"}, "types": "dist/types.d.ts", "source": "src/index.ts", "files": ["dist", "src"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@react-aria/combobox": "^3.12.5", "@react-aria/focus": "^3.20.5", "@react-aria/i18n": "^3.12.10", "@react-aria/interactions": "^3.25.3", "@react-aria/listbox": "^3.14.6", "@react-aria/searchfield": "^3.8.6", "@react-aria/textfield": "^3.17.5", "@react-aria/utils": "^3.29.1", "@react-stately/autocomplete": "3.0.0-beta.2", "@react-stately/combobox": "^3.10.6", "@react-types/autocomplete": "3.0.0-alpha.32", "@react-types/button": "^3.12.2", "@react-types/shared": "^3.30.0", "@swc/helpers": "^0.5.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1", "react-dom": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}, "publishConfig": {"access": "public"}, "gitHead": "a063122082d2b372e4846b58c85ae69ec73887ff"}