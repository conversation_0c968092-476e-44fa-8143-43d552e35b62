/**
 * Health Check API Handlers for MSW
 * 
 * Provides comprehensive mocking for all health check endpoints including:
 * - Simple ping/pong health check
 * - Detailed health status with metrics
 * - Readiness and liveness probes
 * - Performance monitoring endpoints
 */

import { http, HttpResponse } from 'msw';

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export const healthHandlers = [
  /**
   * GET /api/v1/health/ping
   * Simple ping endpoint
   */
  http.get(`${API_BASE}/api/v1/health/ping`, () => {
    return HttpResponse.json({
      message: "pong",
      timestamp: new Date().toISOString(),
      status: "healthy"
    });
  }),

  /**
   * GET /api/v1/health/
   * Basic health check
   */
  http.get(`${API_BASE}/api/v1/health/`, () => {
    return HttpResponse.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      environment: "test"
    });
  }),

  /**
   * GET /api/v1/health/detailed
   * Detailed health check with metrics
   */
  http.get(`${API_BASE}/api/v1/health/detailed`, () => {
    return HttpResponse.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      version: "1.0.0",
      environment: "test",
      checks: {
        database: {
          status: "healthy",
          response_time_ms: 12,
          last_check: new Date().toISOString()
        },
        redis: {
          status: "healthy", 
          response_time_ms: 3,
          last_check: new Date().toISOString()
        },
        external_apis: {
          status: "healthy",
          response_time_ms: 45,
          last_check: new Date().toISOString()
        }
      },
      metrics: {
        uptime_seconds: 86400,
        memory_usage_mb: 256,
        cpu_usage_percent: 15.5,
        active_connections: 42,
        requests_per_minute: 120
      },
      dependencies: {
        python_version: "3.11.0",
        fastapi_version: "0.104.1",
        database_version: "SQLite 3.42.0"
      }
    });
  }),

  /**
   * GET /api/v1/health/ready
   * Readiness probe (Kubernetes style)
   */
  http.get(`${API_BASE}/api/v1/health/ready`, () => {
    return HttpResponse.json({
      status: "ready",
      timestamp: new Date().toISOString(),
      checks: {
        database_connection: "ok",
        migrations_applied: "ok",
        configuration_loaded: "ok"
      }
    });
  }),

  /**
   * GET /api/v1/health/live
   * Liveness probe (Kubernetes style)
   */
  http.get(`${API_BASE}/api/v1/health/live`, () => {
    return HttpResponse.json({
      status: "alive",
      timestamp: new Date().toISOString(),
      uptime_seconds: 86400,
      process_id: 12345
    });
  }),
];