'use client'

import { UserManagement } from '@/components/admin/UserManagement'
import { RouteGuard } from '@/components/auth/RouteGuard'
import { DashboardLayout } from '@/components/layout/DashboardLayout'

export default function AdminUsersPage() {
  return (
    <RouteGuard requireAuth={true} requireAdmin={true}>
      <DashboardLayout title="User Management">
        <UserManagement />
      </DashboardLayout>
    </RouteGuard>
  )
}
