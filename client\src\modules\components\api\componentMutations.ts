/**
 * React Query mutations for component management
 */

import type {
  ComponentBulkCreate,
  ComponentBulkUpdate,
  ComponentCreate,
  ComponentRead,
  ComponentUpdate,
  ComponentValidationResult,
} from '@/types/api'
import { MutationKeys, QueryKeys } from '@/types/api'
import { useMutation, UseMutationOptions, useQueryClient } from '@tanstack/react-query'
import { componentApi } from './componentApi'

// Create component mutation
export function useCreateComponent(
  options?: UseMutationOptions<ComponentRead, Error, ComponentCreate>
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createComponent,
    mutationFn: async (component: ComponentCreate) => {
      const response = await componentApi.create(component)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    onSuccess: (data) => {
      // Invalidate and refetch component lists
      queryClient.invalidateQueries({ queryKey: QueryKeys.components })
      queryClient.invalidateQueries({ queryKey: QueryKeys.componentsStats })

      // Add the new component to the cache
      queryClient.setQueryData(QueryKeys.component(data.id), data)
    },
    ...options,
  })
}

// Update component mutation
export function useUpdateComponent(
  options?: UseMutationOptions<ComponentRead, Error, { id: number; component: ComponentUpdate }>
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.updateComponent,
    mutationFn: async ({ id, component }: { id: number; component: ComponentUpdate }) => {
      const response = await componentApi.update(id, component)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    onSuccess: (data, { id }) => {
      // Update the component in the cache
      queryClient.setQueryData(QueryKeys.component(id), data)

      // Invalidate component lists to reflect changes
      queryClient.invalidateQueries({ queryKey: QueryKeys.components })
      queryClient.invalidateQueries({ queryKey: QueryKeys.componentsStats })
    },
    ...options,
  })
}

// Delete component mutation
export function useDeleteComponent(options?: UseMutationOptions<void, Error, number>) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.deleteComponent,
    mutationFn: async (id: number) => {
      const response = await componentApi.delete(id)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    onSuccess: (_, id) => {
      // Remove the component from the cache
      queryClient.removeQueries({ queryKey: QueryKeys.component(id) })

      // Invalidate component lists
      queryClient.invalidateQueries({ queryKey: QueryKeys.components })
      queryClient.invalidateQueries({ queryKey: QueryKeys.componentsStats })
    },
    ...options,
  })
}

// Bulk create components mutation
export function useBulkCreateComponents(
  options?: UseMutationOptions<ComponentValidationResult[], Error, ComponentBulkCreate>
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.bulkCreateComponents,
    mutationFn: async (data: ComponentBulkCreate) => {
      const response = await componentApi.bulkCreate(data)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    onSuccess: () => {
      // Invalidate all component-related queries
      queryClient.invalidateQueries({ queryKey: QueryKeys.components })
      queryClient.invalidateQueries({ queryKey: QueryKeys.componentsStats })
    },
    ...options,
  })
}

// Bulk update components mutation
export function useBulkUpdateComponents(
  options?: UseMutationOptions<ComponentValidationResult[], Error, ComponentBulkUpdate>
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.bulkUpdateComponents,
    mutationFn: async (data: ComponentBulkUpdate) => {
      const response = await componentApi.bulkUpdate(data)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    onSuccess: (_, { component_ids }) => {
      // Invalidate specific components and lists
      component_ids.forEach((id) => {
        queryClient.invalidateQueries({ queryKey: QueryKeys.component(id) })
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components })
      queryClient.invalidateQueries({ queryKey: QueryKeys.componentsStats })
    },
    ...options,
  })
}

// Bulk delete components mutation
export function useBulkDeleteComponents(
  options?: UseMutationOptions<
    { deleted_count: number; errors: any[] },
    Error,
    { componentIds: number[]; soft_delete?: boolean }
  >
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.bulkDeleteComponents,
    mutationFn: async ({
      componentIds,
      soft_delete,
    }: {
      componentIds: number[]
      soft_delete?: boolean
    }) => {
      const response = await componentApi.bulkDelete(componentIds, { soft_delete })
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    onSuccess: (_, { componentIds }) => {
      // Remove components from cache
      componentIds.forEach((id) => {
        queryClient.removeQueries({ queryKey: QueryKeys.component(id) })
      })

      // Invalidate component lists
      queryClient.invalidateQueries({ queryKey: QueryKeys.components })
      queryClient.invalidateQueries({ queryKey: QueryKeys.componentsStats })
    },
    ...options,
  })
}

// Optimistic update helper for component updates
export function useOptimisticComponentUpdate() {
  const queryClient = useQueryClient()

  return {
    updateComponent: (id: number, updates: Partial<ComponentRead>) => {
      queryClient.setQueryData(QueryKeys.component(id), (old: ComponentRead | undefined) => {
        if (!old) return old
        return { ...old, ...updates }
      })
    },

    revertComponent: (id: number, previousData: ComponentRead) => {
      queryClient.setQueryData(QueryKeys.component(id), previousData)
    },
  }
}

// Prefetch helper for component data
export function usePrefetchComponent() {
  const queryClient = useQueryClient()

  return {
    prefetchComponent: (id: number) => {
      queryClient.prefetchQuery({
        queryKey: QueryKeys.component(id),
        queryFn: async () => {
          const response = await componentApi.getById(id)
          if (response.error) {
            throw new Error(response.error.detail)
          }
          return response.data!
        },
        staleTime: 10 * 60 * 1000, // 10 minutes
      })
    },

    prefetchComponents: (params: any = {}) => {
      queryClient.prefetchQuery({
        queryKey: QueryKeys.componentsList(params),
        queryFn: async () => {
          const response = await componentApi.list(params)
          if (response.error) {
            throw new Error(response.error.detail)
          }
          return response.data!
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
      })
    },
  }
}
