/**
 * MSW handlers for component API endpoints
 */

import { http, HttpResponse } from 'msw'
import {
  createMockComponent,
  filterComponents,
  mockComponentCategories,
  mockComponents,
  mockComponentSuggestions,
  mockComponentTypes
} from '../fixtures/components'

// In-memory storage for components during testing
let components = [...mockComponents]
let nextId = 6

export const componentHandlers = [
  // GET /api/v1/components - List components with filtering
  http.get('/api/v1/components', ({ request }) => {
    const url = new URL(request.url)
    const searchParams = Object.fromEntries(url.searchParams)
    
    // Apply filters
    const filteredComponents = filterComponents({
      search_term: searchParams.search || searchParams.search_term,
      category: searchParams.category,
      manufacturer: searchParams.manufacturer,
      is_preferred: searchParams.is_preferred === 'true' ? true : searchParams.is_preferred === 'false' ? false : undefined,
      is_active: searchParams.is_active === 'true' ? true : searchParams.is_active === 'false' ? false : undefined,
    })
    
    // Pagination
    const page = parseInt(searchParams.page, 10) || 1
    const perPage = parseInt(searchParams.size, 10) || 10
    const startIndex = (page - 1) * perPage
    const endIndex = startIndex + perPage
    const paginatedComponents = filteredComponents.slice(startIndex, endIndex)
    
    return HttpResponse.json({
      items: paginatedComponents,
      total: filteredComponents.length,
      page,
      size: perPage,
      pages: Math.ceil(filteredComponents.length / perPage),
    })
  }),

  // GET /api/v1/components/:id - Get component by ID
  http.get('/api/v1/components/:id', ({ params }) => {
    const { id } = params
    const component = components.find(c => c.id === id)
    
    if (!component) {
      return HttpResponse.json(
        { error: 'Component not found' },
        { status: 404 }
      )
    }
    
    return HttpResponse.json({ data: component })
  }),

  // POST /api/v1/components - Create new component
  http.post('/api/v1/components', async ({ request }) => {
    const body = await request.json() as any
    
    // Basic validation
    if (!body.name || !body.manufacturer || !body.model_number) {
      return HttpResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }
    
    const newComponent = createMockComponent({
      ...body,
      id: (nextId++).toString(),
    })
    
    components.push(newComponent)
    
    return HttpResponse.json({ data: newComponent }, { status: 201 })
  }),

  // PUT /api/v1/components/:id - Update component
  http.put('/api/v1/components/:id', async ({ params, request }) => {
    const { id } = params
    const body = await request.json() as any
    
    const componentIndex = components.findIndex(c => c.id === id)
    if (componentIndex === -1) {
      return HttpResponse.json(
        { error: 'Component not found' },
        { status: 404 }
      )
    }
    
    const updatedComponent = {
      ...components[componentIndex],
      ...body,
      updated_at: new Date().toISOString(),
    }
    
    components[componentIndex] = updatedComponent
    
    return HttpResponse.json({ data: updatedComponent })
  }),

  // DELETE /api/v1/components/:id - Delete component
  http.delete('/api/v1/components/:id', ({ params }) => {
    const { id } = params
    const componentIndex = components.findIndex(c => c.id === id)
    
    if (componentIndex === -1) {
      return HttpResponse.json(
        { error: 'Component not found' },
        { status: 404 }
      )
    }
    
    components.splice(componentIndex, 1)
    
    return HttpResponse.json({ message: 'Component deleted successfully' })
  }),

  // GET /api/v1/component-categories - List component categories
  http.get('/api/v1/component-categories', () => {
    return HttpResponse.json({ data: mockComponentCategories })
  }),

  // GET /api/v1/component-types - List component types
  http.get('/api/v1/component-types', ({ request }) => {
    const url = new URL(request.url)
    const category = url.searchParams.get('category')
    
    let filteredTypes = mockComponentTypes
    
    // Filter by category if specified
    if (category) {
      // Simple category-based filtering (in real app this would be more sophisticated)
      filteredTypes = mockComponentTypes.filter(type => {
        switch (category) {
          case 'RESISTOR':
            return type.value.includes('RESISTOR')
          case 'CAPACITOR':
            return type.value.includes('CAPACITOR')
          case 'INDUCTOR':
            return type.value.includes('INDUCTOR')
          case 'TRANSISTOR':
            return ['BJT', 'MOSFET'].includes(type.value)
          case 'DIODE':
            return type.value.includes('DIODE')
          default:
            return true
        }
      })
    }
    
    return HttpResponse.json({ data: filteredTypes })
  }),

  // GET /api/v1/components/suggestions - Get component suggestions
  http.get('/api/v1/components/suggestions', ({ request }) => {
    const url = new URL(request.url)
    const query = url.searchParams.get('q') || ''
    const field = url.searchParams.get('field') || 'name'
    const limit = parseInt(url.searchParams.get('limit') || '10')
    
    if (query.length < 2) {
      return HttpResponse.json({ data: [] })
    }
    
    const suggestions = mockComponentSuggestions
      .filter(suggestion => suggestion.toLowerCase().includes(query.toLowerCase()))
      .slice(0, limit)
    
    return HttpResponse.json({ data: suggestions })
  }),

  // Bulk operations
  http.post('/api/v1/components/bulk-delete', async ({ request }) => {
    const body = await request.json() as { ids: string[] }
    
    components = components.filter(c => !body.ids.includes(c.id))
    
    return HttpResponse.json({ 
      message: `Successfully deleted ${body.ids.length} components` 
    })
  }),

  http.post('/api/v1/components/bulk-update', async ({ request }) => {
    const body = await request.json() as { 
      ids: string[]
      updates: Partial<typeof mockComponents[0]>
    }
    
    components = components.map(c => {
      if (body.ids.includes(c.id)) {
        return {
          ...c,
          ...body.updates,
          updated_at: new Date().toISOString(),
        }
      }
      return c
    })
    
    return HttpResponse.json({ 
      message: `Successfully updated ${body.ids.length} components` 
    })
  }),
]