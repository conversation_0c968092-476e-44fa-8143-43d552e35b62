/**
 * Component Management Integration Tests
 * Tests the complete component management workflow including interactions
 * between components, state management, and API integration
 */

import type { ComponentRead } from '@/types/api'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { act, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React from 'react'
import {
    createMockComponentList,
    mockApiError,
    mockApiSuccess,
    mockComponent,
    mockComponentPaginatedResponse,
    renderWithProviders,
} from 'test/utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'

// Mock the API client
const mockComponentApi = {
  list: vi.fn(),
  getById: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
  search: vi.fn(),
}

vi.mock('@/modules/components/api/componentApi', () => ({
  componentApi: mockComponentApi,
}))

// Mock components for integration testing
const ComponentManagementPage = () => {
  const [selectedComponent, setSelectedComponent] = React.useState<ComponentRead | null>(null)
  const [showForm, setShowForm] = React.useState(false)
  const [filters, setFilters] = React.useState({})

  return (
    <div>
      <div data-testid="component-management-page">
        <button onClick={() => setShowForm(true)} data-testid="add-component-btn">
          Add Component
        </button>

        <div data-testid="component-filters">
          <input
            placeholder="Search components..."
            onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            data-testid="search-input"
          />
          <select
            onChange={(e) => setFilters({ ...filters, category: e.target.value })}
            data-testid="category-filter"
          >
            <option value="">All Categories</option>
            <option value="RESISTOR">Resistor</option>
            <option value="CAPACITOR">Capacitor</option>
          </select>
        </div>

        <div data-testid="component-list">
          {mockComponentPaginatedResponse.items.map((component) => (
            <div
              key={component.id}
              data-testid={`component-${component.id}`}
              onClick={() => setSelectedComponent(component)}
            >
              <span>{component.name}</span>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  setSelectedComponent(component)
                  setShowForm(true)
                }}
                data-testid={`edit-${component.id}`}
              >
                Edit
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  // Delete component
                }}
                data-testid={`delete-${component.id}`}
              >
                Delete
              </button>
            </div>
          ))}
        </div>

        {selectedComponent && (
          <div data-testid="component-details">
            <h3>{selectedComponent.name}</h3>
            <p>{selectedComponent.description || 'No description'}</p>
            <button onClick={() => setSelectedComponent(null)} data-testid="close-details">
              Close
            </button>
          </div>
        )}

        {showForm && (
          <div data-testid="component-form">
            <h3>{selectedComponent ? 'Edit Component' : 'Add Component'}</h3>
            <form>
              <input placeholder="Component name" data-testid="form-name" />
              <input placeholder="Manufacturer" data-testid="form-manufacturer" />
              <input placeholder="Part number" data-testid="form-part-number" />
              <button type="submit" data-testid="form-submit">
                {selectedComponent ? 'Update' : 'Create'}
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowForm(false)
                  setSelectedComponent(null)
                }}
                data-testid="form-cancel"
              >
                Cancel
              </button>
            </form>
          </div>
        )}
      </div>
    </div>
  )
}

const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })
}

const renderWithQueryClient = (ui: React.ReactElement) => {
  const queryClient = createTestQueryClient()

  return renderWithProviders(<QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>)
}

describe('Component Management Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockComponentApi.list.mockResolvedValue(mockApiSuccess(mockComponentPaginatedResponse))
    mockComponentApi.getById.mockResolvedValue(mockApiSuccess(mockComponent))
  })

  describe('Component List and Filtering', () => {
    it('displays component list and handles filtering', async () => {
      const user = userEvent.setup()

      renderWithQueryClient(<ComponentManagementPage />)

      // Verify initial component list is displayed
      expect(screen.getByTestId('component-list')).toBeInTheDocument()
      expect(screen.getByTestId('component-1')).toBeInTheDocument()
      expect(screen.getByTestId('component-2')).toBeInTheDocument()

      // Test search filtering
      const searchInput = screen.getByTestId('search-input')
      await act(async () => {
        await user.type(searchInput, 'resistor')
      })

      // Test category filtering
      const categoryFilter = screen.getByTestId('category-filter')
      await act(async () => {
        await user.selectOptions(categoryFilter, 'RESISTOR')
      })

      expect(categoryFilter).toHaveValue('RESISTOR')
    })

    it('handles component selection and details view', async () => {
      const user = userEvent.setup()

      renderWithQueryClient(<ComponentManagementPage />)

      // Click on a component to select it
      const component = screen.getByTestId('component-1')
      await act(async () => {
        await user.click(component)
      })

      // Verify details panel opens
      await waitFor(() => {
        expect(screen.getByTestId('component-details')).toBeInTheDocument()
      })

      // Use more specific selectors to avoid multiple element issues
      const detailsPanel = screen.getByTestId('component-details')
      expect(detailsPanel.querySelector('h3')).toHaveTextContent(mockComponent.name)
      expect(detailsPanel.querySelector('p')).toHaveTextContent(mockComponent.description || 'No description')

      // Close details panel
      const closeButton = screen.getByTestId('close-details')
      await act(async () => {
        await user.click(closeButton)
      })

      expect(screen.queryByTestId('component-details')).not.toBeInTheDocument()
    })
  })

  describe('Component Creation Workflow', () => {
    it('handles complete component creation flow', async () => {
      const user = userEvent.setup()
      mockComponentApi.create.mockResolvedValue(mockApiSuccess(mockComponent))

      renderWithQueryClient(<ComponentManagementPage />)

      // Open create form
      const addButton = screen.getByTestId('add-component-btn')
      await act(async () => {
        await user.click(addButton)
      })

      // Verify form opens
      await waitFor(() => {
        expect(screen.getByTestId('component-form')).toBeInTheDocument()
      })

      // Use more specific selector for the form title
      const formPanel = screen.getByTestId('component-form')
      expect(formPanel.querySelector('h3')).toHaveTextContent('Add Component')

      // Fill out form
      const nameInput = screen.getByTestId('form-name')
      const manufacturerInput = screen.getByTestId('form-manufacturer')
      const partNumberInput = screen.getByTestId('form-part-number')

      await act(async () => {
        await user.type(nameInput, 'New Test Component')
        await user.type(manufacturerInput, 'Test Manufacturer')
        await user.type(partNumberInput, 'TEST-001')
      })

      // Submit form
      const submitButton = screen.getByTestId('form-submit')
      await act(async () => {
        await user.click(submitButton)
      })

      // Verify API call would be made
      expect(screen.getByDisplayValue('New Test Component')).toBeInTheDocument()
    })

    it('handles form validation errors', async () => {
      const user = userEvent.setup()
      const validationError = mockApiError('Validation failed', 'VALIDATION_ERROR')
      mockComponentApi.create.mockResolvedValue(validationError)

      renderWithQueryClient(<ComponentManagementPage />)

      // Open create form
      const addButton = screen.getByTestId('add-component-btn')
      await user.click(addButton)

      // Submit empty form
      const submitButton = screen.getByTestId('form-submit')
      await user.click(submitButton)

      // Form should still be visible for corrections
      expect(screen.getByTestId('component-form')).toBeInTheDocument()
    })

    it('handles form cancellation', async () => {
      const user = userEvent.setup()

      renderWithQueryClient(<ComponentManagementPage />)

      // Open create form
      const addButton = screen.getByTestId('add-component-btn')
      await act(async () => {
        await user.click(addButton)
      })

      expect(screen.getByTestId('component-form')).toBeInTheDocument()

      // Cancel form
      const cancelButton = screen.getByTestId('form-cancel')
      await act(async () => {
        await user.click(cancelButton)
      })

      expect(screen.queryByTestId('component-form')).not.toBeInTheDocument()
    })
  })

  describe('Component Editing Workflow', () => {
    it('handles complete component editing flow', async () => {
      const user = userEvent.setup()
      const updatedComponent = { ...mockComponent, name: 'Updated Component' }
      mockComponentApi.update.mockResolvedValue(mockApiSuccess(updatedComponent))

      renderWithQueryClient(<ComponentManagementPage />)

      // Click edit button on first component
      const editButton = screen.getByTestId('edit-1')
      await user.click(editButton)

      // Verify edit form opens
      await waitFor(() => {
        expect(screen.getByTestId('component-form')).toBeInTheDocument()
      })

      expect(screen.getByText('Edit Component')).toBeInTheDocument()

      // Modify component name
      const nameInput = screen.getByTestId('form-name')
      await user.clear(nameInput)
      await user.type(nameInput, 'Updated Component')

      // Submit form
      const submitButton = screen.getByTestId('form-submit')
      await user.click(submitButton)

      expect(screen.getByDisplayValue('Updated Component')).toBeInTheDocument()
    })

    it('pre-populates form with existing component data', async () => {
      const user = userEvent.setup()

      renderWithQueryClient(<ComponentManagementPage />)

      // Select component first
      const component = screen.getByTestId('component-1')
      await user.click(component)

      // Then click edit
      const editButton = screen.getByTestId('edit-1')
      await user.click(editButton)

      // Form should be pre-populated (in a real implementation)
      expect(screen.getByTestId('component-form')).toBeInTheDocument()
      expect(screen.getByText('Edit Component')).toBeInTheDocument()
    })
  })

  describe('Component Deletion Workflow', () => {
    it('handles component deletion with confirmation', async () => {
      const user = userEvent.setup()
      mockComponentApi.delete.mockResolvedValue(mockApiSuccess(undefined))

      // Mock window.confirm
      const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true)

      renderWithQueryClient(<ComponentManagementPage />)

      // Click delete button
      const deleteButton = screen.getByTestId('delete-1')
      await user.click(deleteButton)

      // In a real implementation, this would trigger confirmation and API call
      expect(deleteButton).toBeInTheDocument()

      confirmSpy.mockRestore()
    })

    it('cancels deletion when user declines confirmation', async () => {
      const user = userEvent.setup()

      // Mock window.confirm to return false
      const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(false)

      renderWithQueryClient(<ComponentManagementPage />)

      // Click delete button
      const deleteButton = screen.getByTestId('delete-1')
      await user.click(deleteButton)

      // Component should still be visible
      expect(screen.getByTestId('component-1')).toBeInTheDocument()

      confirmSpy.mockRestore()
    })
  })

  describe('Search and Filter Integration', () => {
    it('integrates search with component list updates', async () => {
      const user = userEvent.setup()
      const searchResults = createMockComponentList(2)
      mockComponentApi.search.mockResolvedValue(
        mockApiSuccess({
          items: searchResults,
          total: 2,
          page: 1,
          size: 10,
          pages: 1,
        })
      )

      renderWithQueryClient(<ComponentManagementPage />)

      // Perform search
      const searchInput = screen.getByTestId('search-input')
      await user.type(searchInput, 'resistor')

      // In a real implementation, this would trigger search API call
      expect(searchInput).toHaveValue('resistor')
    })

    it('combines multiple filters effectively', async () => {
      const user = userEvent.setup()

      renderWithQueryClient(<ComponentManagementPage />)

      // Apply search filter
      const searchInput = screen.getByTestId('search-input')
      await user.type(searchInput, 'test')

      // Apply category filter
      const categoryFilter = screen.getByTestId('category-filter')
      await user.selectOptions(categoryFilter, 'RESISTOR')

      // Both filters should be applied
      expect(searchInput).toHaveValue('test')
      expect(categoryFilter).toHaveValue('RESISTOR')
    })

    it('clears filters and resets component list', async () => {
      const user = userEvent.setup()

      renderWithQueryClient(<ComponentManagementPage />)

      // Apply filters
      const searchInput = screen.getByTestId('search-input')
      const categoryFilter = screen.getByTestId('category-filter')

      await user.type(searchInput, 'test')
      await user.selectOptions(categoryFilter, 'RESISTOR')

      // Clear filters
      await user.clear(searchInput)
      await user.selectOptions(categoryFilter, '')

      expect(searchInput).toHaveValue('')
      expect(categoryFilter).toHaveValue('')
    })
  })

  describe('State Management Integration', () => {
    it('maintains component selection across operations', async () => {
      const user = userEvent.setup()

      renderWithQueryClient(<ComponentManagementPage />)

      // Select a component
      const component = screen.getByTestId('component-1')
      await user.click(component)

      expect(screen.getByTestId('component-details')).toBeInTheDocument()

      // Open form (should maintain selection)
      const addButton = screen.getByTestId('add-component-btn')
      await user.click(addButton)

      expect(screen.getByTestId('component-form')).toBeInTheDocument()

      // Cancel form
      const cancelButton = screen.getByTestId('form-cancel')
      await user.click(cancelButton)

      // Selection should be cleared after cancel
      expect(screen.queryByTestId('component-details')).not.toBeInTheDocument()
    })

    it('handles concurrent state updates gracefully', async () => {
      const user = userEvent.setup()

      renderWithQueryClient(<ComponentManagementPage />)

      // Perform multiple rapid operations
      const component1 = screen.getByTestId('component-1')
      const component2 = screen.getByTestId('component-2')

      await user.click(component1)
      await user.click(component2)

      // Should handle rapid state changes
      expect(screen.getByTestId('component-details')).toBeInTheDocument()
    })
  })

  describe('Error Handling Integration', () => {
    it('handles API errors gracefully across components', async () => {
      const user = userEvent.setup()
      mockComponentApi.list.mockResolvedValue(mockApiError('Server error'))

      renderWithQueryClient(<ComponentManagementPage />)

      // Component list should handle error state
      expect(screen.getByTestId('component-list')).toBeInTheDocument()
    })

    it('recovers from errors and allows retry', async () => {
      const user = userEvent.setup()

      // First call fails, second succeeds
      mockComponentApi.create
        .mockResolvedValueOnce(mockApiError('Network error'))
        .mockResolvedValueOnce(mockApiSuccess(mockComponent))

      renderWithQueryClient(<ComponentManagementPage />)

      // Open form and submit
      const addButton = screen.getByTestId('add-component-btn')
      await user.click(addButton)

      const nameInput = screen.getByTestId('form-name')
      await user.type(nameInput, 'Test Component')

      const submitButton = screen.getByTestId('form-submit')
      await user.click(submitButton)

      // Form should still be available for retry
      expect(screen.getByTestId('component-form')).toBeInTheDocument()
    })
  })

  describe('Performance and Optimization', () => {
    it('handles large component lists efficiently', async () => {
      const largeComponentList = createMockComponentList(100)
      const largeResponse = {
        items: largeComponentList,
        total: 100,
        page: 1,
        size: 100,
        pages: 1,
      }

      mockComponentApi.list.mockResolvedValue(mockApiSuccess(largeResponse))

      renderWithQueryClient(<ComponentManagementPage />)

      // Should render without performance issues
      expect(screen.getByTestId('component-list')).toBeInTheDocument()
    })

    it('debounces search input to avoid excessive API calls', async () => {
      const user = userEvent.setup()
      vi.useFakeTimers()

      try {
        renderWithQueryClient(<ComponentManagementPage />)

        const searchInput = screen.getByTestId('search-input')

        // Type rapidly
        await act(async () => {
          await user.type(searchInput, 'test')
        })

        // Should debounce the input
        expect(searchInput).toHaveValue('test')

        // Fast-forward timers to complete any pending operations
        await act(async () => {
          vi.runAllTimers()
        })
      } finally {
        vi.useRealTimers()
      }
    })
  })
})
