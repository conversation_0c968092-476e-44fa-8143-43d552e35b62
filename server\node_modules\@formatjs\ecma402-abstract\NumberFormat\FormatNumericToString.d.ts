import Decimal from 'decimal.js';
import { NumberFormatDigitInternalSlots } from '../types/number';
/**
 * https://tc39.es/ecma402/#sec-formatnumberstring
 */
export declare function FormatNumericToString(intlObject: Pick<NumberFormatDigitInternalSlots, 'roundingType' | 'minimumSignificantDigits' | 'maximumSignificantDigits' | 'minimumIntegerDigits' | 'minimumFractionDigits' | 'maximumFractionDigits' | 'roundingIncrement' | 'roundingMode' | 'trailingZeroDisplay'>, _x: Decimal): {
    roundedNumber: Decimal;
    formattedString: string;
};
