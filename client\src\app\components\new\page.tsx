'use client'

/**
 * Create Component Page
 * Form for creating new components
 */

import { Button } from '@/components/ui/button'
import { ComponentForm } from '@/modules/components'
import { useCreateComponent } from '@/modules/components/api/componentMutations'
import type { ComponentCreate, ComponentUpdate } from '@/modules/components/types'
import { ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function CreateComponentPage() {
  const router = useRouter()

  // Create mutation
  const createMutation = useCreateComponent({
    onSuccess: (component) => {
      router.push(`/components/${component.id}`)
    },
    onError: (error) => {
      alert(`Failed to create component: ${error.message}`)
    },
  })

  // Handle form submission
  const handleSubmit = (data: ComponentCreate | ComponentUpdate) => {
    // Since this is the "new" page, we only handle ComponentCreate
    createMutation.mutate(data as ComponentCreate)
  }

  // Handle cancel
  const handleCancel = () => {
    router.back()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="border-b border-gray-200 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>

              <div>
                <h1 className="text-xl font-bold text-gray-900">Create New Component</h1>
                <p className="text-sm text-gray-600">
                  Add a new electrical component to the catalog
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="mx-auto max-w-4xl px-4 py-6 sm:px-6 lg:px-8">
        <ComponentForm
          isLoading={createMutation.isPending}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      </div>
    </div>
  )
}
