/**
 * Component Category Fixtures for MSW
 * 
 * Provides mock component category data and management functions for
 * hierarchical category operations in E2E tests.
 */

export interface ComponentCategory {
  id: number;
  name: string;
  description: string | null;
  parent_category_id: number | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  children?: ComponentCategory[];
  path?: string;
  level?: number;
}

/**
 * Mock component categories database with hierarchical structure
 */
export let mockComponentCategories: ComponentCategory[] = [
  {
    id: 1,
    name: "Electrical Components",
    description: "Root category for all electrical components",
    parent_category_id: null,
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: 2,
    name: "Cables & Wiring",
    description: "Various types of electrical cables and wiring solutions",
    parent_category_id: 1,
    is_active: true,
    created_at: "2024-01-02T00:00:00Z",
    updated_at: "2024-01-02T00:00:00Z",
  },
  {
    id: 3,
    name: "Power Cables",
    description: "High voltage and power transmission cables",
    parent_category_id: 2,
    is_active: true,
    created_at: "2024-01-03T00:00:00Z",
    updated_at: "2024-01-03T00:00:00Z",
  },
  {
    id: 4,
    name: "Control Cables",
    description: "Control and instrumentation cables",
    parent_category_id: 2,
    is_active: true,
    created_at: "2024-01-04T00:00:00Z",
    updated_at: "2024-01-04T00:00:00Z",
  },
  {
    id: 5,
    name: "Switches & Controls",
    description: "Electrical switches and control devices",
    parent_category_id: 1,
    is_active: true,
    created_at: "2024-01-05T00:00:00Z",
    updated_at: "2024-01-05T00:00:00Z",
  },
  {
    id: 6,
    name: "Circuit Breakers",
    description: "Various types of circuit breakers and protection devices",
    parent_category_id: 5,
    is_active: true,
    created_at: "2024-01-06T00:00:00Z",
    updated_at: "2024-01-06T00:00:00Z",
  },
  {
    id: 7,
    name: "Transformers",
    description: "Power and distribution transformers",
    parent_category_id: 1,
    is_active: true,
    created_at: "2024-01-07T00:00:00Z",
    updated_at: "2024-01-07T00:00:00Z",
  },
  {
    id: 8,
    name: "Distribution Transformers",
    description: "Medium to low voltage distribution transformers",
    parent_category_id: 7,
    is_active: true,
    created_at: "2024-01-08T00:00:00Z",
    updated_at: "2024-01-08T00:00:00Z",
  },
  {
    id: 9,
    name: "Lighting",
    description: "Electrical lighting components and fixtures",
    parent_category_id: 1,
    is_active: false,
    created_at: "2024-01-09T00:00:00Z",
    updated_at: "2024-01-09T00:00:00Z",
  },
];

let nextCategoryId = Math.max(...mockComponentCategories.map(c => c.id)) + 1;

/**
 * Find category by ID
 */
export function findCategoryById(id: number): ComponentCategory | undefined {
  return mockComponentCategories.find(category => category.id === id);
}

/**
 * Create new category
 */
export function createCategory(categoryData: Omit<ComponentCategory, 'id' | 'created_at' | 'updated_at'>): ComponentCategory {
  const now = new Date().toISOString();
  const newCategory: ComponentCategory = {
    id: nextCategoryId++,
    ...categoryData,
    created_at: now,
    updated_at: now,
  };
  
  mockComponentCategories.push(newCategory);
  return newCategory;
}

/**
 * Update existing category
 */
export function updateCategory(id: number, updates: Partial<Omit<ComponentCategory, 'id' | 'created_at'>>): ComponentCategory {
  const categoryIndex = mockComponentCategories.findIndex(category => category.id === id);
  
  if (categoryIndex === -1) {
    throw new Error(`Category with ID ${id} not found`);
  }

  const updatedCategory = {
    ...mockComponentCategories[categoryIndex],
    ...updates,
    updated_at: new Date().toISOString(),
  };
  
  mockComponentCategories[categoryIndex] = updatedCategory;
  return updatedCategory;
}

/**
 * Delete category (with dependency checking)
 */
export function deleteCategory(id: number): boolean {
  const categoryIndex = mockComponentCategories.findIndex(category => category.id === id);
  
  if (categoryIndex === -1) {
    return false;
  }

  // Check for child categories
  const hasChildren = mockComponentCategories.some(cat => cat.parent_category_id === id);
  if (hasChildren) {
    throw new Error(`Category has child categories and cannot be deleted`);
  }

  // Soft delete by setting inactive
  mockComponentCategories[categoryIndex] = {
    ...mockComponentCategories[categoryIndex],
    is_active: false,
    updated_at: new Date().toISOString(),
  };
  
  return true;
}

/**
 * Build hierarchical tree structure
 */
export function getCategoriesTree(rootId: number | null = null): {
  categories: ComponentCategory[];
  total_categories: number;
  max_depth: number;
} {
  const buildTree = (parentId: number | null, level: number = 0): ComponentCategory[] => {
    const children = mockComponentCategories
      .filter(cat => cat.parent_category_id === parentId && cat.is_active)
      .map(category => ({
        ...category,
        level,
        children: buildTree(category.id, level + 1)
      }));
    
    return children;
  };

  const tree = buildTree(rootId);
  const totalCategories = mockComponentCategories.filter(cat => cat.is_active).length;
  
  // Calculate max depth
  const calculateMaxDepth = (categories: ComponentCategory[]): number => {
    if (categories.length === 0) return 0;
    return 1 + Math.max(...categories.map(cat => calculateMaxDepth(cat.children || [])));
  };
  
  const maxDepth = calculateMaxDepth(tree);

  return {
    categories: tree,
    total_categories: totalCategories,
    max_depth: maxDepth,
  };
}

/**
 * Move category to new parent
 */
export function moveCategory(categoryId: number, newParentId: number | null): ComponentCategory {
  const category = findCategoryById(categoryId);
  if (!category) {
    throw new Error(`Category with ID ${categoryId} not found`);
  }

  // Check for circular reference
  if (newParentId && isDescendant(newParentId, categoryId)) {
    throw new Error(`Cannot move category to its own descendant - would create circular reference`);
  }

  return updateCategory(categoryId, { parent_category_id: newParentId });
}

/**
 * Copy category and optionally its children
 */
export function copyCategory(
  categoryId: number,
  targetParentId: number | null,
  copyChildren: boolean = false,
  nameSuffix: string = ' (Copy)'
): ComponentCategory {
  const category = findCategoryById(categoryId);
  if (!category) {
    throw new Error(`Category with ID ${categoryId} not found`);
  }

  const copiedCategory = createCategory({
    name: category.name + nameSuffix,
    description: category.description,
    parent_category_id: targetParentId,
    is_active: category.is_active,
  });

  if (copyChildren) {
    const children = mockComponentCategories.filter(cat => cat.parent_category_id === categoryId);
    for (const child of children) {
      copyCategory(child.id, copiedCategory.id, true, nameSuffix);
    }
  }

  return copiedCategory;
}

/**
 * Bulk restructure operations
 */
export function bulkRestructureCategories(operations: Array<{
  operation: 'move' | 'copy';
  category_id: number;
  new_parent_id?: number;
  target_parent_id?: number;
  copy_children?: boolean;
  name_suffix?: string;
}>): ComponentCategory[] {
  const results: ComponentCategory[] = [];

  for (const operation of operations) {
    try {
      if (operation.operation === 'move') {
        const result = moveCategory(operation.category_id, operation.new_parent_id || null);
        results.push(result);
      } else if (operation.operation === 'copy') {
        const result = copyCategory(
          operation.category_id,
          operation.target_parent_id || null,
          operation.copy_children || false,
          operation.name_suffix || ' (Copy)'
        );
        results.push(result);
      }
    } catch (error: any) {
      throw new Error(`Operation failed for category ${operation.category_id}: ${error.message}`);
    }
  }

  return results;
}

/**
 * Check if category is descendant of another
 */
function isDescendant(ancestorId: number, descendantId: number): boolean {
  const descendant = findCategoryById(descendantId);
  if (!descendant || !descendant.parent_category_id) {
    return false;
  }

  if (descendant.parent_category_id === ancestorId) {
    return true;
  }

  return isDescendant(ancestorId, descendant.parent_category_id);
}

/**
 * Get paginated categories list
 */
export function getPaginatedCategories(
  skip: number = 0,
  limit: number = 20,
  filters: {
    parent_category_id?: number;
    is_active?: boolean;
  } = {}
): { categories: ComponentCategory[]; total: number } {
  let filteredCategories = mockComponentCategories;
  
  if (filters.parent_category_id !== undefined) {
    filteredCategories = filteredCategories.filter(cat => 
      cat.parent_category_id === filters.parent_category_id
    );
  }
  
  if (filters.is_active !== undefined) {
    filteredCategories = filteredCategories.filter(cat => cat.is_active === filters.is_active);
  }
  
  const total = filteredCategories.length;
  const categories = filteredCategories.slice(skip, skip + limit);
  
  return { categories, total };
}

/**
 * Search categories by name or description
 */
export function searchCategories(
  searchTerm: string,
  skip: number = 0,
  limit: number = 20,
  filters: {
    parent_category_id?: number;
    is_active?: boolean;
  } = {}
): { categories: ComponentCategory[]; total: number } {
  const term = searchTerm.toLowerCase();
  
  let filteredCategories = mockComponentCategories.filter(category => {
    const matchesSearch = category.name.toLowerCase().includes(term) || 
                         (category.description && category.description.toLowerCase().includes(term));
    
    let matchesFilters = true;
    
    if (filters.parent_category_id !== undefined) {
      matchesFilters = matchesFilters && category.parent_category_id === filters.parent_category_id;
    }
    
    if (filters.is_active !== undefined) {
      matchesFilters = matchesFilters && category.is_active === filters.is_active;
    }
    
    return matchesSearch && matchesFilters;
  });
  
  const total = filteredCategories.length;
  const categories = filteredCategories.slice(skip, skip + limit);
  
  return { categories, total };
}

/**
 * Reset categories to initial state
 */
export function resetComponentCategories(): void {
  mockComponentCategories = [
    {
      id: 1,
      name: "Electrical Components",
      description: "Root category for all electrical components",
      parent_category_id: null,
      is_active: true,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    },
    {
      id: 2,
      name: "Cables & Wiring",
      description: "Various types of electrical cables and wiring solutions",
      parent_category_id: 1,
      is_active: true,
      created_at: "2024-01-02T00:00:00Z",
      updated_at: "2024-01-02T00:00:00Z",
    },
    {
      id: 3,
      name: "Power Cables",
      description: "High voltage and power transmission cables",
      parent_category_id: 2,
      is_active: true,
      created_at: "2024-01-03T00:00:00Z",
      updated_at: "2024-01-03T00:00:00Z",
    },
    {
      id: 4,
      name: "Control Cables",
      description: "Control and instrumentation cables",
      parent_category_id: 2,
      is_active: true,
      created_at: "2024-01-04T00:00:00Z",
      updated_at: "2024-01-04T00:00:00Z",
    },
    {
      id: 5,
      name: "Switches & Controls",
      description: "Electrical switches and control devices",
      parent_category_id: 1,
      is_active: true,
      created_at: "2024-01-05T00:00:00Z",
      updated_at: "2024-01-05T00:00:00Z",
    },
    {
      id: 6,
      name: "Circuit Breakers",
      description: "Various types of circuit breakers and protection devices",
      parent_category_id: 5,
      is_active: true,
      created_at: "2024-01-06T00:00:00Z",
      updated_at: "2024-01-06T00:00:00Z",
    },
    {
      id: 7,
      name: "Transformers",
      description: "Power and distribution transformers",
      parent_category_id: 1,
      is_active: true,
      created_at: "2024-01-07T00:00:00Z",
      updated_at: "2024-01-07T00:00:00Z",
    },
    {
      id: 8,
      name: "Distribution Transformers",
      description: "Medium to low voltage distribution transformers",
      parent_category_id: 7,
      is_active: true,
      created_at: "2024-01-08T00:00:00Z",
      updated_at: "2024-01-08T00:00:00Z",
    },
    {
      id: 9,
      name: "Lighting",
      description: "Electrical lighting components and fixtures",
      parent_category_id: 1,
      is_active: false,
      created_at: "2024-01-09T00:00:00Z",
      updated_at: "2024-01-09T00:00:00Z",
    },
  ];
  
  nextCategoryId = Math.max(...mockComponentCategories.map(c => c.id)) + 1;
}