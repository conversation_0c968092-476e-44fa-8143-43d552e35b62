'use client'

import { Foot<PERSON> } from '@/components/common/Footer'
import { Header } from '@/components/common/Header'
import { Breadcrumbs } from '@/components/navigation/Breadcrumbs'
import { Sidebar } from '@/components/navigation/Sidebar'
import { ReactNode, useState } from 'react'

interface DashboardLayoutProps {
  children: ReactNode
  title?: string
  showBreadcrumbs?: boolean
  className?: string
}

export function DashboardLayout({
  children,
  title,
  showBreadcrumbs = true,
  className = '',
}: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header />

      <div className="flex">
        {/* Sidebar */}
        <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

        {/* Main content */}
        <div className="flex-1 lg:ml-64">
          <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
            {/* Mobile menu button */}
            <div className="mb-4 lg:hidden">
              <button
                onClick={() => setSidebarOpen(true)}
                className="rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            </div>

            {/* Breadcrumbs */}
            {showBreadcrumbs && (
              <div className="mb-6">
                <Breadcrumbs />
              </div>
            )}

            {/* Page title */}
            {title && (
              <div className="mb-6">
                <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
              </div>
            )}

            {/* Page content */}
            <div className={className}>{children}</div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  )
}
