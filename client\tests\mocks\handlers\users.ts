/**
 * User Management API Handlers for MSW
 *
 * Provides comprehensive mocking for user management including:
 * - Admin-only CRUD operations for users
 * - Profile fetching and updates for authenticated users
 * - Role-based access control for all user-related actions
 * - Pagination and search for user lists
 */

import { http, HttpResponse } from 'msw';
import {
    AuthErrors,
    getUserFromRequest,
    isAdminUser,
    MockUser,
    UserUtils,
} from '../fixtures/auth';

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export const userHandlers = [
  /**
   * GET /api/v1/users/
   * Get all users (admin only)
   */
  http.get(`${API_BASE}/api/v1/users/`, ({ request }) => {
    const currentUser = getUserFromRequest(request);
    if (!isAdminUser(currentUser)) {
      return HttpResponse.json(AuthErrors.ADMIN_REQUIRED, { status: 403 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const size = parseInt(url.searchParams.get('size') || '20');
    const role = url.searchParams.get('role') || undefined;
    const isActive = url.searchParams.get('is_active');
    const search = url.searchParams.get('search') || undefined;

    const skip = (page - 1) * size;
    const { users, total } = UserUtils.getPaginatedUsers(skip, size, {
      role,
      is_active: isActive ? isActive === 'true' : undefined,
      search,
    });

    const sanitizedUsers = users.map(user => {
      const { password_hash, ...rest } = user;
      return rest;
    });

    return HttpResponse.json({
      users: sanitizedUsers,
      total,
      page,
      size,
      total_pages: Math.ceil(total / size),
    });
  }),

  /**
   * GET /api/v1/users/{user_id}
   * Get user by ID (admin or self)
   */
  http.get(`${API_BASE}/api/v1/users/:userId`, ({ request, params }) => {
    const currentUser = getUserFromRequest(request);
    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 });
    }

    const userId = parseInt(params.userId as string);
    if (!isAdminUser(currentUser) && currentUser.id !== userId) {
      return HttpResponse.json(AuthErrors.INSUFFICIENT_PERMISSIONS, { status: 403 });
    }

    const user = UserUtils.findById(userId);
    if (!user) {
      return HttpResponse.json({ error: 'USER_NOT_FOUND', detail: 'User not found' }, { status: 404 });
    }

    const { password_hash, ...userResponse } = user;
    return HttpResponse.json(userResponse);
  }),

  /**
   * PUT /api/v1/users/{user_id}
   * Update user (admin or self)
   */
  http.put(`${API_BASE}/api/v1/users/:userId`, async ({ request, params }) => {
    const currentUser = getUserFromRequest(request);
    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 });
    }

    const userId = parseInt(params.userId as string);
    const updates = (await request.json()) as Partial<MockUser>;

    if (isAdminUser(currentUser)) {
      // Admins can update any user
    } else if (currentUser.id === userId) {
      // Non-admins can only update their own non-privileged fields
      delete updates.role;
      delete updates.is_active;
    } else {
      return HttpResponse.json(AuthErrors.INSUFFICIENT_PERMISSIONS, { status: 403 });
    }

    const updatedUser = UserUtils.updateUser(userId, updates);
    if (!updatedUser) {
      return HttpResponse.json({ error: 'USER_NOT_FOUND', detail: 'User not found' }, { status: 404 });
    }

    const { password_hash, ...userResponse } = updatedUser;
    return HttpResponse.json(userResponse);
  }),

  /**
   * DELETE /api/v1/users/{user_id}
   * Delete user (admin only)
   */
  http.delete(`${API_BASE}/api/v1/users/:userId`, ({ request, params }) => {
    const currentUser = getUserFromRequest(request);
    if (!isAdminUser(currentUser)) {
      return HttpResponse.json(AuthErrors.ADMIN_REQUIRED, { status: 403 });
    }

    const userId = parseInt(params.userId as string);
    if (currentUser?.id === userId) {
      return HttpResponse.json(
        { error: 'CANNOT_DELETE_SELF', detail: 'Admin cannot delete their own account' },
        { status: 400 }
      );
    }

    const success = UserUtils.deleteUser(userId);
    if (!success) {
      return HttpResponse.json({ error: 'USER_NOT_FOUND', detail: 'User not found' }, { status: 404 });
    }

    return new HttpResponse(null, { status: 204 });
  }),
];